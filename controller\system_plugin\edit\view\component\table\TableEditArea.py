# coding=utf-8
'''
Created on 2019年12月10日

@author: 10247557
'''
from _functools import partial

from PyQt5.Qt import QTableWidgetItem, QTimer
from PyQt5.QtWidgets import QTextEdit

from controller.system_plugin.SignalDistributor import SignalDistributor
from controller.system_plugin.edit.view.component.table.Colorizer import Colorizer
from utility.log.SystemLogger import logger


# from controller.system_plugin.SignalDistributor import SignalDistributor
class TableEditArea():

    def __init__(self, table_obj):
        self._table_obj = table_obj
        self._table = table_obj._table
        self._set_table_edit_area()
        self._add_action()
        self._line_edit_flag = True

    def get_edit(self):
        return self._table_edit_area

    def _set_table_edit_area(self):
        self._table_edit_area = QTextEdit()
        self._table_edit_area.setFontPointSize(12)

        # 设置最小和最大高度
        self._min_height = 40
        self._max_height = 200
        self._table_edit_area.setMinimumHeight(self._min_height)
        self._table_edit_area.setMaximumHeight(self._max_height)

        # 初始设置为最小高度
        self._table_edit_area.setFixedHeight(self._min_height)

        self._set_edit_area_style()
        self._table_edit_area.textChanged.connect(self._fill_table_item)
        self._table_edit_area.textChanged.connect(self._adjust_height)

        # 连接主题变化信号
        self._connect_theme_signal()

    def _set_edit_area_style(self):
        """设置编辑区域样式"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            current_theme = theme_manager.get_current_theme()
            colors = theme_manager.get_theme_colors(current_theme)

            if current_theme == 'eye_protect_green':
                # 墨绿色主题：使用深墨绿色背景，与编辑器保持一致
                style = f"""
                    QTextEdit {{
                        border: none;
                        background-color: {colors['editor_bg']};
                        color: {colors['editor_text']};
                        selection-background-color: {colors['editor_selection_bg']};
                        selection-color: {colors['editor_selection_text']};
                        font-size: 12pt;
                    }}
                """
            elif current_theme == 'dark':
                # 深色主题：使用更深的背景色，与主题一致但略微浅一些
                style = f"""
                    QTextEdit {{
                        border: none;
                        background-color: {colors['editor_bg']};
                        color: {colors['editor_text']};
                        selection-background-color: {colors['editor_selection_bg']};
                        selection-color: {colors['editor_selection_text']};
                        font-size: 12pt;
                    }}
                """
            elif current_theme == 'cool_blue':
                # 淡蓝色主题
                style = f"""
                    QTextEdit {{
                        border: none;
                        background-color: {colors['editor_bg']};
                        color: {colors['editor_text']};
                        selection-background-color: {colors['editor_selection_bg']};
                        selection-color: {colors['editor_selection_text']};
                        font-size: 12pt;
                    }}
                """
            else:
                # 浅色主题（默认）
                style = f"""
                    QTextEdit {{
                        border: none;
                        background-color: {colors['editor_bg']};
                        color: {colors['editor_text']};
                        selection-background-color: {colors['editor_selection_bg']};
                        selection-color: {colors['editor_selection_text']};
                        font-size: 12pt;
                    }}
                """

            self._table_edit_area.setStyleSheet(style)
        except Exception as e:
            print(f"设置表格编辑区域样式失败: {e}")
            # 降级处理：使用基本样式
            self._table_edit_area.setStyleSheet("border: none;background-color: #ececec;color: #000000;")

    def _connect_theme_signal(self):
        """连接主题变化信号"""
        try:
            from controller.system_plugin.style.ThemeManager import ThemeManager
            theme_manager = ThemeManager()
            theme_manager.theme_changed.connect(self._on_theme_changed)
        except Exception as e:
            print(f"连接表格编辑区域主题信号失败: {e}")

    def _on_theme_changed(self, theme_id):
        """主题变化时的回调"""
        self._set_edit_area_style()

    def _adjust_height(self):
        """根据文本内容自动调整编辑框高度"""
        try:
            # 使用QTimer延迟调整，确保文档大小已更新
            QTimer.singleShot(0, self._do_adjust_height)
        except Exception as e:
            print(f"调整编辑框高度失败: {e}")

    def _do_adjust_height(self):
        """实际执行高度调整"""
        try:
            # 获取文档高度
            document = self._table_edit_area.document()
            document_height = document.size().height()

            # 计算需要的高度（加上一些边距）
            needed_height = int(document_height) + 15

            # 限制在最小和最大高度之间
            if needed_height < self._min_height:
                needed_height = self._min_height
            elif needed_height > self._max_height:
                needed_height = self._max_height

            # 只有当高度发生变化时才调整
            current_height = self._table_edit_area.height()
            if current_height != needed_height:
                self._table_edit_area.setFixedHeight(needed_height)

        except Exception as e:
            print(f"执行高度调整失败: {e}")

    def _add_action(self):
        self._table.clicked.connect(partial(self._fill_table_edit_area, self))
        self._table.currentItemChanged.connect(partial(self._fill_table_edit_area, self))
        SignalDistributor().modify_table_item.connect(self._fill_table_edit_area_by_table_item)

    @staticmethod
    def _fill_table_edit_area(self):
        select_value = self._table.selectedItems()
        if select_value:
            row = self._table.selectedIndexes()[0].row()
            column = self._table.selectedIndexes()[0].column()
            item = self._table.item(row, column)
            if item:
                text = item.text()
                self._table_edit_area.setPlainText(text)
                # 设置文本后调整高度
                self._adjust_height()
        else:
            self._table_edit_area.setPlainText("")
            # 清空文本后也调整高度
            self._adjust_height()

    def _fill_table_edit_area_by_table_item(self, text):
        self._line_edit_flag = False
        self._table_edit_area.setPlainText(text)
        # 设置文本后调整高度
        self._adjust_height()
        self._line_edit_flag = True

    def _get_data(self):
        text = self._table_edit_area.toPlainText()
        return text.replace('\n', '')

    def _fill_table_item(self):
        
        if self._line_edit_flag:
            table_edit_area_text = self._get_data()
            table_item_text = ''
            try:
                row = self._table.selectedIndexes()[0].row()
                column = self._table.selectedIndexes()[0].column()
                item = self._table.item(row, column)
                if item:
                    table_item_text = item.text()
                if table_item_text != table_edit_area_text:
                    item = QTableWidgetItem(table_edit_area_text)
                    self._table.setItem(int(row), int(column), item)
            except Exception as e:
                logger.warn(e)
