#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试中文字符显示和光标定位修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt5.QtCore import Qt
from PyQt5.Qsci import QsciScintilla

class ChineseTextEditor(QsciScintilla):
    def __init__(self):
        super().__init__()
        
        # 设置UTF-8编码和中文字符支持
        self.setUtf8(True)
        
        # 设置字符编码模式，确保正确处理多字节字符
        self.SendScintilla(QsciScintilla.SCI_SETCODEPAGE, QsciScintilla.SC_CP_UTF8)
        
        # 设置字符宽度缓存，提高中文字符渲染性能
        self.SendScintilla(QsciScintilla.SCI_SETCHARACTERCATEGORYOPTIMIZATION, 0x100)
        
        # 设置双字节字符支持
        self.SendScintilla(QsciScintilla.SCI_SETIMEINTERACTION, QsciScintilla.SC_IME_WINDOWED)
        
        # 设置选择模式，确保中文字符选择正确
        self.SendScintilla(QsciScintilla.SCI_SETSELECTIONMODE, QsciScintilla.SC_SEL_STREAM)
        
        print("中文字符支持设置完成")

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("中文字符显示和光标定位测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("""
测试说明：
1. 下面的编辑器包含中文字符和标点符号
2. 请测试选择文本时是否有空白问题
3. 请测试删除键是否能正确删除光标前的字符
4. 请测试退格键是否能正确删除光标后的字符
5. 观察语法高亮是否正常显示
        """)
        layout.addWidget(info_label)
        
        # 创建测试按钮
        test_button = QPushButton("重置测试内容")
        test_button.clicked.connect(self.reset_content)
        layout.addWidget(test_button)
        
        # 创建文本编辑器
        self.editor = ChineseTextEditor()
        layout.addWidget(self.editor)
        
        # 设置测试内容
        self.reset_content()

    def reset_content(self):
        """重置测试内容"""
        test_content = """*** Test Cases ***
测试用例1【中文标点符号】
    Log    这是一个包含中文的测试步骤，包含各种标点符号：，。！？；：""''（）【】
    Should Be Equal    ${变量名}    期望值
    
测试用例2（英文标点符号）
    Log    This is a test step with English punctuation: ,.!?;:"'()[]
    Should Contain    ${text}    substring

*** Keywords ***
我的自定义关键字
    [Documentation]    这是一个包含中文文档的关键字
    Log    执行自定义关键字：测试中文字符的显示和编辑
    Return From Keyword    成功

英文关键字
    [Documentation]    This is an English keyword
    Log    Executing English keyword
    Return From Keyword    success

*** Variables ***
${中文变量}    中文值
${ENGLISH_VAR}    English value
${混合_MIXED_变量}    混合值 Mixed Value

*** Settings ***
Documentation    测试套件文档：包含中文字符的Robot Framework测试文件
Library          SeleniumLibrary
Resource         ../resources/中文资源.robot
Variables        ../variables/中文配置.py

# 这是中文注释行
# This is English comment line
# 这是混合注释 Mixed comment 包含中英文
"""
        self.editor.setText(test_content)
        print("测试内容已重置")

def main():
    """运行测试"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    print("中文字符测试应用已启动")
    print("请进行以下测试：")
    print("1. 选择包含中文字符的文本，观察是否有空白问题")
    print("2. 在中文字符前后使用删除键和退格键")
    print("3. 观察光标定位是否准确")
    print("4. 测试中文标点符号的处理")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
