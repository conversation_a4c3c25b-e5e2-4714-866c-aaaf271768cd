import datetime
import os
from queue import Queue
import re
import sys
import time
import traceback

from PyQt5.Qt import pyqtSignal
from robot.output import L<PERSON>VE<PERSON>

from controller.system_plugin.run.PrintTraceLogSwitch import PrintTraceLogSwitch
from controller.test_runner.BaseProfile import BaseProfile
from controller.test_runner.ProgressBar import ProgressBar
from controller.test_runner.PybotProfile import PybotProfile
from controller.test_runner.RealTimeXml import RealTimeXml
from controller.test_runner.TestRunner import TestRunner
from settings.LogProject import LogProject
from utility.LogPathRepository import LogPathRepository
from utility.PluginRepository import PluginRepository


ON_POSIX = 'posix' in sys.builtin_module_names


class TestRunnerPlugin():

    """A plugin for running tests from within RFCODE"""

    def __init__(self, application=None):
        self._test_runner = TestRunner()
        self._min_log_level_number = LEVELS['INFO']
        self.progress_bar = ProgressBar()
        self.messages_log_texts = Queue()
        self.real_time_xml = None
        self._show_real_time_log = False

    def enable(self):
        self._read_run_profiles()
        self._test_runner.enable(self._post_result)

    def clear_messages_log_texts(self):
        while not self.messages_log_texts.empty():
            self.messages_log_texts.get(False)

    def _read_run_profiles(self):
        self._read_run_profiles_from_classes()

    def get_test_state(self):
        return self._test_runner.testState

    def _read_run_profiles_from_classes(self):
        for profile in self._get_all_subclasses(BaseProfile):
            self._test_runner.add_profile(profile.name, profile(plugin=self))

    def _get_all_subclasses(self, class_):
        classes = []
        for sub_class in class_.__subclasses__():
            classes += [sub_class] + self._get_all_subclasses(sub_class)
        return classes

    def on_close(self, evt):
        '''Shut down the running services and processes'''
        self._test_runner.kill_process()
        self._test_runner.shutdown_server()

    def on_stop(self):
        self._test_runner.send_stop_signal()

    def on_pause(self):
        self._test_runner.send_pause_signal()

    def on_continue(self):
        self._test_runner.send_continue_signal()

    def on_step_next(self, event):
        self._test_runner.send_step_next_signal()

    def on_step_over(self, event):
        self._test_runner.send_step_over_signal()

    def on_run(self, run_args):
        # Store selected test cases
        if 'testcases' in run_args:
            PluginRepository().update('SELECTED_TESTCASES', run_args['testcases'])
            
        self._run_args = run_args
        command = self._create_command()
        print(f"Running command: {command}")
        self._test_runner.run_command(command, self._get_current_working_dir())
        self._set_running()
        self._show_real_time_log = PluginRepository().find('SHOW_REAL_TIME_LOG')
        self.real_time_xml = RealTimeXml()

    def _create_command(self):
        command_as_list = self._test_runner.get_command(self._run_args)
        self._min_log_level_number = self._test_runner.get_message_log_level(command_as_list)
        command = self._format_command(command_as_list)
        return command

    def _get_current_working_dir(self):
        return self._run_args.get('workdir')

    def _format_command(self, argv):
        result = []
        for arg in argv:
            if "'" in arg or " " in arg:
                result.append('"%s"' % arg)
            elif '"' in arg:
                result.append("'%s'" % arg)
            else:
                result.append(arg)
        return " ".join(result)

    def _get_monitor_width(self):
        return "85"

    def get_current_profile(self):
        return self._test_runner.get_profile("pybot")

    def _post_result(self, event, *args):
        try:
            if event == 'start_suite':
                self._handle_start_suite(args)
            if event == 'end_suite':
                self._handle_end_suite(args)
            if event == 'start_test':
                self._handle_start_test(args)
            if event == 'end_test':
                self._handle_end_test(args)
            if event == 'report_file':
                self._handle_report_file(args)
            if event == 'log_file':
                self._handle_log_file(args)
            if event == 'start_keyword':
                self._handle_start_keyword(args)
            if event == 'end_keyword':
                self._handle_end_keyword(args)
            if event == 'log_message':
                self._handle_log_message(args)
        except Exception:
            traceback.print_exc()

    def _handle_start_suite(self, args):
        print(f"Starting suite: {args[1]['longname']}")
        if self._show_real_time_log:
            self.real_time_xml.add_start_suite_element(args)

    def _handle_end_suite(self, args):
        print(f"Ending suite: {args[1]['longname']}")
        if self._show_real_time_log:
            self.real_time_xml.add_end_suite_element(args)

    def _handle_start_test(self, args):
        longname = args[1]['longname']
        print(f"Starting test: {longname}")
        self._append_to_message_log('Starting test: %s' % longname)
        if self._show_real_time_log:
            self.real_time_xml.add_start_test_element(args)

    def _append_to_message_log(self, text):
        pass

    def _handle_end_test(self, args):
        longname = args[1]['longname']
        status = args[1]['status']
        print(f"Ending test: {longname} with status: {status}")
        self._append_to_message_log('Ending test:   %s\n' % longname)
        if status == 'PASS':
            self.progress_bar.add_pass()
        else:
            self.progress_bar.add_fail()
        if self._show_real_time_log:
            self.real_time_xml.add_end_test_elements(args)

    def _handle_report_file(self, args):
        self._report_file = args[0]

    def _handle_log_file(self, args):
        self._log_file = args[0]
        LogPathRepository().add('LOG_GENERATED', True)

    def _handle_start_keyword(self, args):
        self.progress_bar.set_current_keyword(args[0])
        if self._show_real_time_log:
            self.real_time_xml.add_start_keyword_elements(args)

    def _handle_end_keyword(self, args):
        self.progress_bar.empty_current_keyword()
        if self._show_real_time_log:
            self.real_time_xml.add_end_keyword_element(args)

    def _handle_log_message(self, args):
        a = args[0]
        self.show_message_log = True
        if self.show_message_log and LEVELS[a['level']] >= self._min_log_level_number:
            prefix = '%s : %s : ' % (a['timestamp'], a['level'].rjust(5))
            message = a['message']
            if '\n' in message:
                message = '\n' + message
            if PrintTraceLogSwitch().is_open():
                self.messages_log_texts.put(prefix + message)
            else:
                self.clear_messages_log_texts()
            if self._show_real_time_log and self.real_time_xml:
                self.real_time_xml.add_log_message_element(args)

    def _set_running(self):
        self._running = True
        self._test_runner.test_execution_started()

if __name__ == "__main__":
    run_args = {'testcases': [['TestCalc.test', 'TestCalc.test.娴嬭瘯璁℃暟鍣ㄥ噺娉�'], ['TestCalc.test', 'TestCalc.test.娴嬭瘯璁℃暟鍣ㄥ姞娉�']], 'workdir': 'D:\program\RFTest\TestCalc'}
    plugin = TestRunnerPlugin()
    plugin.enable()
    plugin.on_run(run_args)
    for i in range(20):
        print("aaaaaaaaaaaaaaaaaaaaaaaaaaaatimes:%s" % (i))
        print("00000", plugin.progress_bar.update_message())
        print("11111", plugin.get_test_state())
        output, errors = plugin._test_runner.get_output_and_errors()
        if output:
            print(output)
        time.sleep(1)
